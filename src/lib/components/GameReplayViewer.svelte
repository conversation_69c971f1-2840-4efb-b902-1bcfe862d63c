<!--
  GameReplayViewer Component
  
  Interactive game replay showing step-by-step move visualization:
  - Navigate through moves with controls
  - See board state before/after each move
  - Highlight selected tiles for each move
  - Show move details and scoring
-->

<script lang="ts">
	import BoardVisualization from './BoardVisualization.svelte';
	import type { EnhancedMove } from '$lib/types';

	interface Props {
		/** Array of enhanced moves to replay */
		moves: EnhancedMove[];
		/** Initial board state */
		initialBoard?: any[][];
		/** Whether to auto-play the replay */
		autoPlay?: boolean;
		/** Auto-play speed in milliseconds */
		autoPlaySpeed?: number;
	}

	let { moves, initialBoard, autoPlay = false, autoPlaySpeed = 2000 }: Props = $props();

	// Current move index (-1 = initial state, 0+ = after move N)
	let currentMoveIndex = $state(-1);
	let isPlaying = $state(false);
	let playInterval: ReturnType<typeof setInterval> | null = null;

	// Current board state to display
	let currentBoard = $derived(() => {
		if (currentMoveIndex === -1) {
			// Show initial board or first move's "before" state
			return initialBoard || moves[0]?.boardBefore || [];
		} else if (currentMoveIndex < moves.length) {
			// Show board after the current move
			return moves[currentMoveIndex]?.boardAfter || [];
		} else {
			// Show final state
			return moves[moves.length - 1]?.boardAfter || [];
		}
	});

	// Current move being highlighted
	let currentMove = $derived(() => {
		if (currentMoveIndex >= 0 && currentMoveIndex < moves.length) {
			return moves[currentMoveIndex];
		}
		return null;
	});

	// Selected positions for current move
	let selectedPositions = $derived(() => {
		const move = currentMove();
		return move?.positions || [];
	});

	// Navigation functions
	function goToMove(index: number) {
		currentMoveIndex = Math.max(-1, Math.min(moves.length - 1, index));
	}

	function nextMove() {
		if (currentMoveIndex < moves.length - 1) {
			currentMoveIndex++;
		} else if (isPlaying) {
			// Loop back to start when auto-playing
			currentMoveIndex = -1;
		}
	}

	function prevMove() {
		if (currentMoveIndex > -1) {
			currentMoveIndex--;
		}
	}

	function togglePlay() {
		isPlaying = !isPlaying;

		if (isPlaying) {
			playInterval = setInterval(nextMove, autoPlaySpeed);
		} else {
			if (playInterval) {
				clearInterval(playInterval);
				playInterval = null;
			}
		}
	}

	// Cleanup on destroy
	function cleanup() {
		if (playInterval) {
			clearInterval(playInterval);
		}
	}

	// Auto-start if requested
	$effect(() => {
		if (autoPlay && !isPlaying) {
			togglePlay();
		}

		return cleanup;
	});

	// Calculate cumulative score up to current move
	let cumulativeScore = $derived(() => {
		if (currentMoveIndex < 0) return 0;
		return moves.slice(0, currentMoveIndex + 1).reduce((sum, move) => sum + move.score, 0);
	});
</script>

<div class="game-replay-viewer rounded-lg bg-white p-6 shadow-lg">
	<!-- Header -->
	<div class="mb-6 flex items-center justify-between">
		<h3 class="text-xl font-semibold text-gray-900">Game Replay</h3>
		<div class="text-sm text-gray-600">
			{moves.length} moves • {moves.reduce((sum, move) => sum + move.score, 0)} total points
		</div>
	</div>

	<!-- Current Move Info -->
	<div class="mb-6 rounded-lg bg-gray-50 p-4">
		{#if currentMoveIndex === -1}
			<div class="text-center">
				<h4 class="font-medium text-gray-900">Initial Board State</h4>
				<p class="text-sm text-gray-600">Ready to start the game</p>
			</div>
		{:else}
			{@const move = currentMove()}
			{#if move}
				<div class="grid grid-cols-1 gap-4 text-center md:grid-cols-3">
					<div>
						<div class="text-2xl font-bold text-blue-600">{move.word}</div>
						<div class="text-sm text-gray-600">Move {currentMoveIndex + 1}</div>
					</div>
					<div>
						<div class="text-2xl font-bold text-green-600">{move.score}</div>
						<div class="text-sm text-gray-600">Points</div>
					</div>
					<div>
						<div class="text-2xl font-bold text-purple-600">{cumulativeScore}</div>
						<div class="text-sm text-gray-600">Total Score</div>
					</div>
				</div>

				<!-- Tile positions -->
				<div class="mt-3 text-center">
					<div class="text-sm text-gray-600">
						Selected tiles: {move.positions.map(([r, c]) => `[${r},${c}]`).join(', ')}
					</div>
				</div>
			{/if}
		{/if}
	</div>

	<!-- Board Display -->
	<div class="mb-6">
		{#if currentBoard().length > 0}
			{@const board = currentBoard()}
			{@const positions = selectedPositions()}
			<BoardVisualization
				{board}
				selectedPositions={positions}
				showMultipliers={true}
				tileSize={56}
				animated={true}
				class="mx-auto"
			/>
		{:else}
			<div class="py-8 text-center text-gray-500">
				<p>Board state not available</p>
			</div>
		{/if}
	</div>

	<!-- Controls -->
	<div class="flex items-center justify-center space-x-4">
		<!-- Previous Move -->
		<button
			onclick={prevMove}
			disabled={currentMoveIndex <= -1}
			class="rounded-lg border border-gray-300 p-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
			title="Previous Move"
		>
			<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
			</svg>
		</button>

		<!-- Play/Pause -->
		<button
			onclick={togglePlay}
			class="rounded-lg bg-blue-600 p-3 text-white transition-colors hover:bg-blue-700"
			title={isPlaying ? 'Pause' : 'Play'}
		>
			{#if isPlaying}
				<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6" />
				</svg>
			{:else}
				<svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
					/>
				</svg>
			{/if}
		</button>

		<!-- Next Move -->
		<button
			onclick={nextMove}
			disabled={currentMoveIndex >= moves.length - 1}
			class="rounded-lg border border-gray-300 p-2 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
			title="Next Move"
		>
			<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
			</svg>
		</button>
	</div>

	<!-- Move Timeline -->
	<div class="mt-6">
		<div class="flex items-center justify-center space-x-2">
			<!-- Initial state -->
			<button
				onclick={() => goToMove(-1)}
				class="h-3 w-3 rounded-full border-2 transition-colors {currentMoveIndex === -1
					? 'border-blue-600 bg-blue-600'
					: 'border-gray-300 hover:border-gray-400'}"
				title="Initial State"
			></button>

			<!-- Move indicators -->
			{#each moves as move, index}
				<button
					onclick={() => goToMove(index)}
					class="h-3 w-3 rounded-full border-2 transition-colors {currentMoveIndex === index
						? 'border-blue-600 bg-blue-600'
						: 'border-gray-300 hover:border-gray-400'}"
					title="Move {index + 1}: {move.word} ({move.score} pts)"
				></button>
			{/each}
		</div>

		<!-- Timeline labels -->
		<div class="mt-2 flex justify-between text-xs text-gray-500">
			<span>Start</span>
			<span>Move {moves.length}</span>
		</div>
	</div>
</div>
